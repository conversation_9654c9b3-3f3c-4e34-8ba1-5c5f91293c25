package com.maguo.loan.cash.flow.common;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum ResultCode {
    SUCCESS("000000", "success"),
    PARAM_ILLEGAL("100000", "参数不合法"),
    BIZ_ERROR("100001", "业务异常"),
    APPLY_SUSPEND("100002", "服务升级中,请稍后发起"),
    SYSTEM_UPGRADE("100004", "系统升级中,请稍后再试"),
    APP_UPGRADE("100005", "系统升级中，请下载应用最新版本"),
    NO_SUBMIT_REPEAT("555555", "请勿重复提交", false),
    APPROVAL_APPLY_REPEAT("600000", "进件频率限制,请稍后重试", false),
    TOO_MANY_REQUEST("600429", "请求过于频繁，请10秒后重试"),
    CREDIT_ERROR("777777", "授信异常"),
    LOAN_ERROR("888888", "放款异常"),
    SYS_ERROR("999999", "系统异常"),
    FILE_UPLOAD_ERROR("444444", "文件上传异常"),
    SYS_MAINTAIN("900000", "系统维护"),
    LOGIN_CHECK_ERROR("100003", "用户未登录,请重新登录"),
    LOGIN_AMOUNT_CHECK_ERROR("100003", "额度更新中，请重新登录后再试"),
    LOGIN_FAIL("100099", "登录失败"),

    USER_NOT_EXIST("100004", "用户不存在"),
    USER_LOG_OFF("10099", "抱歉，此账号已注销。如有疑问，请联系客服。"),
    RISK_RECORD_NOT_EXIST("100005", "风控记录不存在"),
    USER_LOAN_CARD_NOT_EXIST("100006", "用户借款卡不存在"),
    USER_FACE_NOT_EXIST("100007", "人脸信息不存在"),
    USER_OCR_NOT_EXIST("100008", "OCR信息不存在"),
    USER_INFO_NOT_EXIST("100009", "用户授信信息不存在"),
    USER_TOKEN_NOT_EXIST("100010", "用户登录信息异常"),
    REPAY_REPEAT_ERROR("100011", "权益包已发起扣款,请稍后重试"),
    RISK_IMG_NOT_EXIST("100012", "风控影像文件不存在"),
    USER_EXPAND_NOT_EXIST("100013", "用户扩展不存在"),
    BORUI_ACTIVATION_FAIL("100014", "放款成功博瑞激活异常"),
    ID_CARD_NOT_EXIST("100021", "身份扫描信息证不存在"),
    CREDIT_AMOUNT_ERROR("100021", "授信金额小于申请金额"),
    CERT_NOT_VALIDITY_FAIL("100023", "您的身份证有效期不符合借款要求，请重新上传"),
    CERT_VALIDITY_FAIL("100024", "身份证信息24小时内最多修改三次"),
    CERT_NO_VALIDITY_FAIL("100025", "身份证号与当前用户不匹配"),
    ORDER_STATE_VALIDITY_FAIL("100026", "当前存在在途进件不允许修改身份证信息"),
    URORA_LOGIN_FAIL("100027", "一键登录失败，请重新登录或尝试其他登录方式"),
    ORDER_NOT_EXIST("200001", "订单不存在"),
    SIGNATURE_RECORD_IS_NULL_ERROR("201325", "签章文件不存在"),
    ROUTER_RECORD_NOT_EXIST("200008", "路由记录不存在"),
    ROUTER_RECORD_CHECK_NOT_EXIST("200008", "路由检测记录不存在"),
    ROURE_CAPITAL_CONFIG_NOT_EXIST("200009", "路由资金规则不存在"),
    ROURE_FLOW_RULE_NOT_EXIST("200010", "路由流量规则不存在"),
    CREDIT_NOT_EXIST_SUCCEED("200002", "不存在成功的授信记录"),
    CREDIT_NOT_FOUND("200003", "未找到授信记录"),
    CREDIT_REPETITION_SUBMIT("200004", "授信重复提交"),
    RIGHTS_ORDER_NOT_EXIST("200005", "权益订单不存在"),
    RIGHTS_REPAY_RECORD_NOT_SUCCEED("200006", "权益还款不成功"),
    RIGHTS_ORDER_REPEAT("200007", "权益订单重复提交"),
    CREDIT_NOT_SUCCEED("200008", "授信未成功,不能发起放款"),
    CAPITAL_CONFIG_FAIL("200008", "资方配置不存在"),
    ORDER_STATE_ERROR("200009", "订单状态异常"),
    NOT_FAIL_LOAN_RECORD_EXIST("200010", "存在非失败的放款记录,无法发起放款"),
    NO_BIND_CARD_SUBMIT_LOAN("200011", "请先绑卡再提交要款", false),
    NO_BIND_SECOND_CARD_SUBMIT_LOAN("200012", "请进行二次绑卡后再提交要款", false),
    REPAY_NOT_EXIST("300001", "还款记录不存在"),
    REPAY_CHECK_ERROR("300002", "还款处理中", false),
    REPAY_PROCESSING_RECORD_NOT_EXIST("300003", "还款记录不存在（母）"),
    REPAY_FEE_NOT_EXIST("300004", "费用记录不存在"),
    REPAY_PLAN_NOT_EXIST("300005", "费用计划不存在"),
    REPAY_TIME_CHECK_FAIL("300006", "当前时间不能还款", false),
    REPAY_TRAIL_CORE_FAIL("300007", "对客计划已还", false),
    REPAY_TRAIL_CORE_PROCESSING_FAIL("300008", "还款处理中,请稍后试算", false),
    NOW_BELONG_REPAY_BATCH("300009", "系统正在批扣中,请稍后再进行操作", false),
    REPAY_AMOUNT_ERROR("300010", "还款金额有误,请联系客服"),
    REPAY_TRAIL_CORE_ACCOUNT_FAIL("300011", "上期银行尚未入账，不支持试算/还款", false),
    REPAY_TRAIL_PROCESSING_FAIL("300013", "上期还款银行正在处理中，请稍后再试", false),
    REPAY_TIME_CORE_ACCOUNT_FAIL("300012", "还款时间限制", false),
    REPAY_NOT_SUPPORT("300014", "暂不支持还款", false),
    TRIAL_HTTP_TIMEOUT("300015", "还款试算超时，请稍后再试", false),
    REPAY_CLEAR_TIME_CHECK_FAIL("300016", "当前时间不能提前结清", false),
    RIGHTS_PACKAGE_NOT_EXIST("400001", "基础权益包不存在"),
    RIGHTS_CONFIG_NOT_EXIST("400002", "权益配置不存在"),
    GET_JW_URL_ERROR("400003", "获取权益兑换页失败"),
    RIGHTS_CODE_NOT_EXIST("400004", "权益code获取失败"),
    RIGHTS_REFUND_NOT_SUPPORT("400005", "当前权益暂不支持退款", false),
    CARD_ID_NOT_EXIST("500001", "银行卡不存在"),
    CARD_NOT_SUPPORT("500002", "不支持的银行卡", false),
    CARD_RECORD_NOT_EXIST("500003", "绑卡记录不存在"),
    CARD_RECORD_RELATION_NOT_EXIST("500004", "绑卡关联关系不存在"),
    CARD_APPLY_FAIL("500005", "绑卡申请失败", false),
    CARD_CONFIRM_FAIL("500006", "绑卡确认失败", false),
    CARD_RECORD_AGREEMENT_NOT_EXIST("500006", "绑卡协议号不存在"),
    LOAN_NOT_EXIST("600001", "借据不存在"),
    LOAN_RECORD_NOT_EXIST("600001", "放款记录不存在"),
    LOAN_NOT_SUCCEED("600002", "借据不成功"),
    LOAN_MULTI_NOT_ALLOW("600003", "在途借据不支持多笔"),
    LOAN_RISK_REJECT_30_DAYS("600007", "30天内风控拒绝"),
    LOAN_REPLAN_NOT_EXIST("600004", "还款计划不存在"),
    LOAN_REPLAN_NOT_SUPPORT("600005", "还款计划生成不支持"),
    LOAN_REPLAN_NOT_FAIL("600005", "查询不到还款计划"),
    LOAN_ALREADY_EXIST("600006", "借据已存在"),
    REPAY_TRAIL_FAIL("700001", "试算失败"),
    REPPAY_PLAN_CHECK_ERROR("600001", "还款计划校验异常,请重试"),
    REPAY_NOT_SUPPORTED_CURRENT("700002", "不支持提前还当期,请到账单日发起还款", false),
    REPAY_APPLY_REQUEST_FAIL("700003", "还款申请失败"),
    REPAY_PLAN_NORMAL_NOT_EXIST("700004", "未查询到待还记录"),
    REPAY_NOT_SUPPORTED_REPAY_TIME("700005", "当前时间段不支持发起还款"),
    REPAY_CLEAR_NOT_SUPPORTED_REPAY_DATE("700006", "还款日当天不允许发起提前结清还款", false),
    REPAY_NOT_SUPPORTED_LOAN_DATE("700006", "放款日当天不允许发起还款", false),
    REPAY_NOT_RIGHTS_DEDUCT_STATE("700007", "温馨提示：您已在借款时同意购买会员权益，请先支付会员优花卡，再发起提前结清"),
    REPAY_CLEAR_NOT_SUPPORTED_OVERDUE("700008", "逾期不允许发起提前结清还款", false),
    REPAY_CLEAR_NOT_SUPPORTED("700009", "当期有未扣除费用, 不允许提前结清"),
    REPAY_OFFLINE_ERROR("700010", "线下还款异常"),
    REPAY_OFFLINE_REDUCE_ERROR("700011", "线下还款减免计算异常"),
    REPAY_QUERY_ERROR("700012", "还款查询异常"),
    REPAY_PERIOD_ERROR("700013", "还款期数有误"),
    AGGREGATED_PAY_RECORD_NOT_EXIST("700014", "聚合支付记录信息不存在"),
    AGGREGATED_PAYMENT_EXPIRED("700015", "聚合支付申请已过期"),
    CALLBACK_PARAM_EXCEPTION("700016", "回调参数异常"),
    AGGREGATED_PAYMENT_EXCEPTION("700017", "聚合支付还款异常"),
    SUCCESS_PAYMENT_EXCEPTION("700018", "该笔订单已支付成功，请勿重复操作"),
    APP_STATION_MSG_NOT_EXIST("700019", "站内消息不存在"),
    PAYMENT_FLOW_NOT_EXIST("700020", "支付流水信息不存在"),
    URL_EXPIRED("700021", "当前链接已失效,请联系客服"),
    CURRENT_PERIOD_REPAID("700022", "当期已还", false),
    REPAY_NOT_TRIAL("700023", "借据状态不允许此操作", false),
    SYS_INTERNAL_ANOMALIES_FAIL("700024", "系统内部异常，请联系管理员", false),
    REPAY_ELEMENTS_ISNULL_ERROR("700101", "通过项目唯一编码查询产品要素记录为空。请检查！", false),
    REPAY_DARK_PERIOD_ERROR("700102", "当前时间为还款黑暗期，不允许还款", false),
    REPAY_YEAR_PERIOD_ERROR("700103", "当前日期时间为年结期间，不允许还款", false),
    REPAY_ELEMENTS_EXT_ISNULL_ERROR("700104", "通过项目唯一编码查询产品要素扩展记录为空。请检查！", false),
    REPAY_TEMP_TIME_ISNULL_ERROR("700105", "年结是否顺延为是时，产品要素配置的临时配置有效期起和临时配置有效期止不能为空，请检查！", false),
    REPAY_PROJECT_CODE_ISNULL_ERROR("700106", "项目唯一编码为空。请检查！", false),
    REPAY_GRACE_PERIOD_DAYS_ISNULL_ERROR("700107", "产品要素扩展记录配置的逾期宽限期(天)为空。请检查！", false),
    // 优品账户相关响应码
    REPEATEDLY_REQUEST_SEND_SMS("100004", "重复发送短信请求"),
    REQUEST_SEND_SMS_ERROR("100005", "发送短信异常,请重新再试!"),
    REPEATEDLY_REQUEST_CONFIRM_SMS("100006", "验证码不正确!"),
    REQUEST_CONFIRM_SMS_ERROR("100007", "验证码验证异常,请重新再试!"),
    USER_ACCOUNT_NOT_EXIST("100008", "用户未注册!"),
    USER_ACCOUNT_CHECK_ERROR("100009", "账户信息异常!"),
    INCONSISTENT_PASSWORD_CHECK_ERROR("100010", "重复输入的密码不一致!"),
    REPEATEDLY_PASSWORD_CHECK_ERROR("100011", "原密码和新密码不能重复!"),
    LOGIN_PASSWORD_CHECK_ERROR("100012", "登录密码错误!"),
    RESET_UPDATE_FAILURE("100013", "密码重置失败!"),
    JW_LOGIN_TOKEN_QUERY_FAILURE("100014", "及未登录异常!"),
    WY_LOGIN_TOKEN_QUERY_FAILURE("100045", "纬雅登录异常!"),
    SEND_MSG_SCENE_NOT_SUPPORTED("100015", "当前场景不支持发送短信!"),
    USER_ACCOUNT_STATE_NOT_SUPPORTED("100016", "当前账户状态异常!"),
    RESET_UPDATE_INITIAL_PASSWORD_FAILURE("100018", "初始密码设置失败!"),
    USER_REPEAT_SET_INITIAL_PASSWORD("100017", "用户已注册!"),
    USER_ACCOUNT_PASSWORD_ERROR("100019", "密码错误!"),
    ACCOUNT_LOGIN_OVERTIME_FAILURE("100020", "用户登录超时!"),
    USER_ACCOUNT_CANCEL_FAILURE("100013", "用户申请注销失败!"),
    CUSTOMER_SERVICE_TOKEN_CHECK_ERROR("100014", "权益包获取失败,token已失效!"),
    WEIYAN_REQUEST_FAILURE("100030", "微言请求失败!"),
    CAPTCHA_FAIL("100031", "滑动验证码验证失败"),
    CAPTCHA_RESULT_FAIL("100032", "滑动验证码验证失败", false),

    // OSS_FILE_NOT_EXIST
    OSS_FILE_NOT_EXIST("100031", "OSS文件不存在"),

    LIVENESS_FAILURE("100032", "获取活体检测失败"),

    ID_CARD_OCR_FAIL("100033", "身份证OCR识别失败"),
    BANK_CARD_OCR_FAIL("100037", "银行卡OCR识别失败"),

    NOT_SUPPORTED_RIGHTS("100015", "服务器异常,请联系人工"),
    // 联系人绑定失败
    CONTACT_BIND_FAILURE("100016", "联系人绑定失败"),
    QUERY_RECORDS_FAIL("100017", "查询不到记录"),

    GUARANTEE_AMT_NOT_EXIST("100018", "担保费未生成"),
    // 风控权益记录不存在
    RISK_CONNECT_ERROR("100032", "风控服务异常"),
    RISK_RECORD_NOT_EXIST_ERROR("100033", "风控记录不存在"),
    BAIWEI_RISK_RECORD_ERROR("100033", "调用百维接口返回异常"),
    FLOW_CONFIG_NOT_EXIST("100034", "流量配置不存在"),
    PRE_ORDER_NOT_EXIST("100035", "预订单记录不存在", false),
    USER_BANK_CARD_FAIL("100036", "查询不到绑卡记录"),
    RIGHTS_REPAY_PLAN_NOT_SUCCEED("200006", "权益扣款计划查询不到"),
    RIGHTS_SUPPLIER_NOT_SUPPORT("20007", "权益供应商不支持"),
    RIGHTS_REPAY_CANCEL("20008", "权益扣款已取消", false),
    RIGHTS_REPAY_REFUNDED("20009", "权益已退款", false),
    APPLY_SIGN_FAIL("100037", "协议签署异常"),
    DB_FAIL("100038", "数据异常"),
    // 年龄格式错误
    AGE_FORMAT_ERROR("800001", "年龄格式错误"),

    // 金额范围格式错误
    AMOUNT_RANGE_FORMAT_ERROR("800002", "金额范围格式错误"),
    //LPR查询失败
    LPR_QUERY_FAIL("900001", "LPR查询失败"),
    //AGREEMENT_QUERY_FAIL
    AGREEMENT_QUERY_FAIL("900002", "协议查询失败"),
    //尚未下载完成
    NOT_DOWNLOAD_COMPLETE("900004", "尚未下载完成"),
    DOWNLOAD_FAIL("900003", "下载失败"),
    DOWNLOAD_IMAGE_FAIL("900005", "下载图片失败"),

    ALREADY_BIND_CAPITAL_CARD("900006", "您已提交放款，请刷新当前页面"),
    USER_CAPI_BANK_CARD_ERROR("100038", "资方绑卡记录申请失败"),

    SERVICE_UPGRADE("900010", "服务已升级,请重新进入当前页面"),
    AREA_CODE_NOT_EXIST("900007", "地区编码不存在"),
    COLLISION_RECORD_NOT_FOUND("900008", "撞库记录不存在"),
    COUPON_NOT_EXIST("900009", "优惠券不存在"),
    USE_STATE_EXCEPTION("9000011", "使用状态异常"),
    CHECK_CONTACT_PHONE_EXCEPTION("100027", "请输入正确的手机号"),
    PHONE_EQUAL_EXCEPTION("100028", "紧急联系人手机号码不能与注册手机号相同"),
    TEMPLATE_NOT_EXIST("1000001", "短信模版不存在"),
    GET_DWZ_ERROR("3000001", "短网址转换失败"),
    PRE_LOAN_NOT_EXIST("3000002", "贷前审核记录不存在"),
    ID_CARD_CHECK_FAIL("100029", "身份证二要素校验失败", false),
    REPEATEDLY_REQUEST_OCR("100030", "重复请求"),
    TRANS_PAY_QUERY_ERROR("100031", "转账支付查询失败"),
    RIGHTS_REPAY_RECORD_NOT_PROCESSING("100032", "权益还款不在处理中"),
    RIGHTS_REPAY_ROUTE_NOT_EXIST("100033", "权益路由记录不存在"),
    RIGHTS_REPAY_ROUTE_PROCESSING("100034", "存在处理中权益路由"),
    RIGHTS_REPAY_REPEAT("100035", "权益路由记录已全部走完，请勿重复推送"),
    ROURE_FLOW_CONFIG_NOT_EXIST("100036", "当前流量配置限额异常"),
    NO_LIMIT_DAY_LOAN_FLOW_CHANNEL("100037", "头寸不足"),
    AGREEMENT_NOT_CONFIG("`100038", "合同模板配置表不存在"),
    SUBMIT_SIGN_FAILED("100039", "提交签署申请失败"),
    PROJECT_NOT_CONFIG("100040", "项目配置不存在"),
    BANK_CARD_NOT_FOUND("100041", "银行卡不存在"),

    REPAYMENT_TYPE_REQUIRED("700001", "还款类型不能为空"),
    UNSUPPORTED_REPAYMENT_TYPE("700002", "不支持的还款类型"),
    PROJECT_ELEMENTS_NOT_FOUND("700004", "未找到项目要素"),
    LOAN_TERMS_FORMAT_ERROR("700005", "借款期限格式错误"),
    LOAN_TERM_NOT_IN_ALLOWED_RANGE("700006", "借款期限不在允许范围内"),
    DRAWABLE_AMOUNT_STEP_INVALID("700007", "单笔提现步长必须大于0"),
    DRAWABLE_AMOUNT_STEP_NOT_MATCH("700008", "提现金额不是步长的整数倍"),
    TIME_SUSPEND_LOAN_FLOW_CHANNEL_ERROR("700010", "当前放款处于黑暗期"),
    DRAWABLE_AMOUNT_STEP_FORMAT_ERROR("700009", "单笔提现步长格式错误"), ;


    private final String code;
    private final String msg;

    /**
     * 是否告警,默认告警
     * 在全局异常拦截器判断该字段为true时，则告警
     */
    private final Boolean isWarning;

    ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.isWarning = true;
    }

    ResultCode(String code, String msg, Boolean isWarning) {
        this.code = code;
        this.msg = msg;
        this.isWarning = isWarning;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }

    public Boolean getWarning() {
        return isWarning;
    }
}
